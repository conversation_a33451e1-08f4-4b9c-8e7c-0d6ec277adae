import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { FaTimes, FaSave } from "react-icons/fa";
import { MdCancel } from "react-icons/md";

/**
 * Compact modal for adding/editing channel mappings
 * Uses a slide-over style modal that doesn't take full screen
 */
const MappingModal = ({
  isOpen,
  onClose,
  onSave,
  editingMapping = null,
  isLoading = false,
  reportTypes = [],
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm();

  // Populate form when editing
  useEffect(() => {
    if (editingMapping) {
      setValue("reportType", editingMapping.reportType);
      setValue("slackChannelId", editingMapping.slackChannelId);
      setValue("description", editingMapping.description || "");
    } else {
      reset();
    }
  }, [editingMapping, setValue, reset]);

  const handleFormSubmit = (data) => {
    onSave(data);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
            <div className="pointer-events-auto relative w-screen max-w-md">
              <div className="flex h-full flex-col overflow-y-scroll bg-white dark:bg-gray-800 shadow-xl">
                {/* Header */}
                <div className="bg-blue-600 px-4 py-6 sm:px-6">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-medium text-white">
                      {editingMapping
                        ? "Edit Channel Mapping"
                        : "Add Channel Mapping"}
                    </h2>
                    <button
                      type="button"
                      className="rounded-md bg-blue-600 text-blue-200 hover:text-white focus:outline-none focus:ring-2 focus:ring-white"
                      onClick={handleClose}
                    >
                      <span className="sr-only">Close panel</span>
                      <FaTimes className="h-6 w-6" />
                    </button>
                  </div>
                  <div className="mt-1">
                    <p className="text-sm text-blue-200">
                      {editingMapping
                        ? "Update the channel mapping configuration"
                        : "Configure a new report type to Slack channel mapping"}
                    </p>
                  </div>
                </div>

                {/* Form */}
                <form
                  onSubmit={handleSubmit(handleFormSubmit)}
                  className="flex-1 flex flex-col"
                >
                  <div className="flex-1 px-4 py-6 sm:px-6">
                    <div className="space-y-6">
                      {/* Report Type */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Report Type *
                        </label>
                        <select
                          {...register("reportType", {
                            required: "Report type is required",
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        >
                          <option value="">Select report type</option>
                          {reportTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                        {errors.reportType && (
                          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                            {errors.reportType.message}
                          </p>
                        )}
                      </div>

                      {/* Slack Channel ID */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Slack Channel ID *
                        </label>
                        <input
                          type="text"
                          {...register("slackChannelId", {
                            required: "Channel ID is required",
                            pattern: {
                              value: /^[CGDW][A-Z0-9]{8,}$/,
                              message:
                                "Invalid channel ID format (should start with C, G, D, or W)",
                            },
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          placeholder="C1234567890"
                        />
                        {errors.slackChannelId && (
                          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                            {errors.slackChannelId.message}
                          </p>
                        )}
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          Find channel ID in Slack: Right-click channel → View
                          channel details
                        </p>
                      </div>

                      {/* Description */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Description (Optional)
                        </label>
                        <textarea
                          {...register("description")}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          placeholder="Brief description of this mapping"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 px-4 py-4 sm:px-6">
                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={handleClose}
                        className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                      >
                        <MdCancel />
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        {isLoading ? (
                          <span className="loading loading-spinner loading-sm"></span>
                        ) : (
                          <FaSave />
                        )}
                        {isLoading ? "Saving..." : "Save Mapping"}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MappingModal;
