const axios = require('./server-side/node_modules/axios');

async function testChannelMapping() {
  try {
    console.log('🧪 Testing channel mapping API...');
    
    // Test data - using a valid Slack channel ID format
    const testData = {
      reportType: 'daily_work',
      slackChannelId: 'C1234567890', // This is a fake but valid format channel ID
      description: 'Test channel mapping'
    };
    
    console.log('📤 Sending request with data:', testData);
    
    // Make the API call (without authentication for now)
    const response = await axios.post('http://localhost:500/api/integrations/slack/mappings', testData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Success:', response.data);
    
  } catch (error) {
    console.log('❌ Error occurred:');
    console.log('Status:', error.response?.status);
    console.log('Status Text:', error.response?.statusText);
    console.log('Error Data:', error.response?.data);
    console.log('Error Message:', error.message);
    
    if (error.response?.data?.details) {
      console.log('Validation Details:', error.response.data.details);
    }
  }
}

testChannelMapping();
