const { MongoClient } = require("./server-side/node_modules/mongodb");

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "wpdevDB";

async function checkSlackIntegration() {
  const client = new MongoClient(MONGODB_URI);

  try {
    console.log("🔌 Connecting to MongoDB...");
    await client.connect();

    const db = client.db(DB_NAME);
    console.log("✅ Connected to MongoDB");

    // Check slack_integrations collection
    const integrationCollection = db.collection("slack_integrations");
    const integrations = await integrationCollection.find({}).toArray();

    console.log("\n📊 Slack Integrations:");
    console.log(`Total integrations: ${integrations.length}`);

    if (integrations.length > 0) {
      integrations.forEach((integration, index) => {
        console.log(`\nIntegration ${index + 1}:`);
        console.log(`  ID: ${integration._id}`);
        console.log(`  Team ID: ${integration.teamId}`);
        console.log(`  Team Name: ${integration.teamName}`);
        console.log(`  Is Active: ${integration.isActive}`);
        console.log(`  Connection Status: ${integration.connectionStatus}`);
        console.log(`  Has Bot Token: ${!!integration.botToken}`);
        console.log(`  Connected At: ${integration.connectedAt}`);
        console.log(`  Last Test At: ${integration.lastTestAt}`);
      });

      // Check for active integration
      const activeIntegration = await integrationCollection.findOne({
        isActive: true,
        connectionStatus: "connected",
      });

      console.log(
        `\n🔍 Active Integration: ${activeIntegration ? "Found" : "Not Found"}`
      );
      if (activeIntegration) {
        console.log(`  Team: ${activeIntegration.teamName}`);
        console.log(`  Status: ${activeIntegration.connectionStatus}`);
      }
    } else {
      console.log("❌ No Slack integrations found in database");
      console.log("💡 You need to set up a Slack integration first");
    }

    // Check channel mappings
    const mappingCollection = db.collection("slack_channel_mappings");
    const mappings = await mappingCollection.find({}).toArray();

    console.log(`\n📋 Channel Mappings: ${mappings.length} found`);
    if (mappings.length > 0) {
      mappings.forEach((mapping, index) => {
        console.log(
          `  ${index + 1}. ${mapping.reportType} -> ${
            mapping.slackChannelId
          } (${mapping.slackChannelName || "No name"})`
        );
      });
    }

    // Check users
    const userCollection = db.collection("users");
    const users = await userCollection.find({}).toArray();

    console.log(`\n👥 Users: ${users.length} found`);
    if (users.length > 0) {
      users.forEach((user, index) => {
        console.log(
          `  ${index + 1}. ${user.email} (${user.role || "no role"})`
        );
      });
    }
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await client.close();
    console.log("\n🔌 Disconnected from MongoDB");
  }
}

checkSlackIntegration();
